import requests
import json
import os

def query_groq():
    """
    Simple function that queries Groq API with "hey wassup cuh" and prints the response
    """
    # Get API key from environment variable
    api_key = os.getenv('GROQ_API_KEY')
    if not api_key:
        print("Error: Please set your GROQ_API_KEY environment variable")
        print("You can get an API key from: https://console.groq.com/keys")
        print("Then run: export GROQ_API_KEY='your_api_key_here'")
        return
    
    # Groq API endpoint
    url = "https://api.groq.com/openai/v1/chat/completions"
    
    # Request payload
    payload = {
        "messages": [
            {
                "role": "user",
                "content": "hey wassup cuh"
            }
        ],
        "model": "llama-3.1-70b-versatile",  # Using Llama 3.1 70B model
        "temperature": 0.7,
        "max_tokens": 1024
    }
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    try:
        print("Sending query to Groq...")
        response = requests.post(url, headers=headers, data=json.dumps(payload))
        
        if response.status_code == 200:
            result = response.json()
            if 'choices' in result and len(result['choices']) > 0:
                text_response = result['choices'][0]['message']['content']
                print("\nGroq's response:")
                print("-" * 40)
                print(text_response)
                print("-" * 40)
            else:
                print("No response generated")
                print("Full response:", result)
        else:
            print(f"Error: HTTP {response.status_code}")
            print("Response:", response.text)
            
    except Exception as e:
        print(f"Error querying Groq: {e}")

if __name__ == "__main__":
    query_groq()
