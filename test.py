import google.generativeai as genai
import os

def query_gemini():
    """
    Simple function that queries Gemini 2.5 Flash with "hey wassup cuh" and prints the response
    """
    # Configure the API key (you'll need to set this as an environment variable)
    api_key = "AIzaSyCHAl8Qd7sSMDsih6nctP7Voce4HFiZXl8"
    if not api_key:
        print("Error: Please set your GOOGLE_API_KEY environment variable")
        return

    genai.configure(api_key=api_key)

    # Initialize the model (Gemini 2.5 Flash)
    model = genai.GenerativeModel('gemini-2.0-flash-lite')

    # Send the query
    try:
        response = model.generate_content("hey wassup cuh")
        print("Gemini's response:")
        print(response.text)
    except Exception as e:
        print(f"Error querying Gemini: {e}")

if __name__ == "__main__":
    query_gemini()