import requests
import json
import os

def query_gemini():
    """
    Simple function that queries Gemini using REST API with "hey wassup cuh" and prints the response
    """
    # Get API key from environment variable
    api_key = os.getenv('GOOGLE_API_KEY')
    if not api_key:
        print("Error: Please set your GOOGLE_API_KEY environment variable")
        print("You can get an API key from: https://aistudio.google.com/app/apikey")
        print("Then run: export GOOGLE_API_KEY='your_api_key_here'")
        return

    # Gemini API endpoint
    url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={api_key}"

    # Request payload
    payload = {
        "contents": [
            {
                "parts": [
                    {
                        "text": "hey wassup cuh"
                    }
                ]
            }
        ]
    }

    headers = {
        "Content-Type": "application/json"
    }

    try:
        print("Sending query to Gemini...")
        response = requests.post(url, headers=headers, data=json.dumps(payload))

        if response.status_code == 200:
            result = response.json()
            if 'candidates' in result and len(result['candidates']) > 0:
                text_response = result['candidates'][0]['content']['parts'][0]['text']
                print("\nGemini's response:")
                print("-" * 40)
                print(text_response)
                print("-" * 40)
            else:
                print("No response generated")
                print("Full response:", result)
        else:
            print(f"Error: HTTP {response.status_code}")
            print("Response:", response.text)

    except Exception as e:
        print(f"Error querying Gemini: {e}")

if __name__ == "__main__":
    query_gemini()